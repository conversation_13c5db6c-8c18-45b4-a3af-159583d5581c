2025-06-16 19:10:12.224 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-16 19:10:12.270 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-16 19:10:12.272 +03:00 [INF] Hosting environment: Development
2025-06-16 19:10:12.273 +03:00 [INF] Content root path: D:\AIProjectTest\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-16 19:10:15.404 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/ - null null
2025-06-16 19:10:15.450 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/ - 404 0 null 47.811ms
2025-06-16 19:10:15.487 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/, Response status code: 404
2025-06-16 19:10:19.915 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-16 19:10:19.916 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-16 19:10:19.928 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 404 0 null 13.3581ms
2025-06-16 19:10:19.944 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 404 0 null 28.0029ms
2025-06-16 19:10:19.944 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js, Response status code: 404
2025-06-16 19:10:19.949 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/_vs/browserLink, Response status code: 404
2025-06-16 19:10:20.232 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-16 19:10:20.362 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 130.0207ms
2025-06-16 19:10:28.489 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-16 19:10:28.632 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 143.3691ms
2025-06-16 19:10:28.727 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-16 19:10:28.744 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 16.039ms
2025-06-16 19:10:36.316 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner - null null
2025-06-16 19:10:36.322 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API)'
2025-06-16 19:10:36.345 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-16 19:10:40.427 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-16 19:10:40.457 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-16 19:10:40.506 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API) in 4154.3119ms
2025-06-16 19:10:40.508 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API)'
2025-06-16 19:10:40.517 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner - 404 null application/json; charset=utf-8 4200.6003ms
2025-06-16 19:11:33.425 +03:00 [INF] Application is shutting down...
2025-06-16 19:27:55.682 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-16 19:27:56.468 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-16 19:27:56.473 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-16 19:27:56.474 +03:00 [INF] Hosting environment: Development
2025-06-16 19:27:56.476 +03:00 [INF] Content root path: D:\AIProjectTest\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-16 19:27:59.844 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/ - null null
2025-06-16 19:27:59.923 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/ - 404 0 null 79.6762ms
2025-06-16 19:27:59.964 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/, Response status code: 404
2025-06-16 19:28:03.475 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-16 19:28:03.562 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 87.2455ms
2025-06-16 19:28:03.907 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-16 19:28:04.037 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 130.0775ms
2025-06-16 19:28:28.278 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 48
2025-06-16 19:28:28.285 +03:00 [INF] CORS policy execution successful.
2025-06-16 19:28:28.290 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-16 19:28:28.311 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-16 19:28:31.110 +03:00 [INF] Executed DbCommand (45ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-16 19:28:31.147 +03:00 [WRN] Login attempt with invalid password for user: Admin
2025-06-16 19:28:31.156 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-16 19:28:31.187 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 2870.3665ms
2025-06-16 19:28:31.190 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-16 19:28:31.197 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 401 null application/json; charset=utf-8 2919.2826ms
2025-06-16 19:28:39.714 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 48
2025-06-16 19:28:39.721 +03:00 [INF] CORS policy execution successful.
2025-06-16 19:28:39.725 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-16 19:28:39.727 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-16 19:28:39.818 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-16 19:28:39.822 +03:00 [WRN] Login attempt with invalid password for user: Admin
2025-06-16 19:28:39.824 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-16 19:28:39.826 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 97.04ms
2025-06-16 19:28:39.828 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-16 19:28:39.831 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 401 null application/json; charset=utf-8 117.1074ms
2025-06-16 19:29:23.262 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 48
2025-06-16 19:29:23.265 +03:00 [INF] CORS policy execution successful.
2025-06-16 19:29:23.266 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-16 19:29:23.268 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-16 19:29:23.309 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-16 19:29:23.313 +03:00 [ERR] Error verifying password
System.FormatException: The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters.
   at System.Convert.FromBase64CharPtr(Char* inputPtr, Int32 inputLength)
   at System.Convert.FromBase64String(String s)
   at GamalComapany.Service.Authentication.PasswordHashingService.VerifyPassword(String password, String hashedPassword) in D:\AIProjectTest\GamalCompanyApp\GamalComapnyApp\GamalComapany.Service\Authentication\PasswordHashingService.cs:line 61
2025-06-16 19:29:23.328 +03:00 [WRN] Login attempt with invalid password for user: Admin
2025-06-16 19:29:23.329 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-16 19:29:23.330 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 59.1163ms
2025-06-16 19:29:23.331 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-16 19:29:23.332 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 401 null application/json; charset=utf-8 70.1637ms
2025-06-16 19:29:38.100 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 48
2025-06-16 19:29:38.104 +03:00 [INF] CORS policy execution successful.
2025-06-16 19:29:38.105 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-16 19:29:38.106 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-16 19:29:38.158 +03:00 [INF] Executed DbCommand (45ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-16 19:29:38.161 +03:00 [ERR] Error verifying password
System.FormatException: The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters.
   at System.Convert.FromBase64CharPtr(Char* inputPtr, Int32 inputLength)
   at System.Convert.FromBase64String(String s)
   at GamalComapany.Service.Authentication.PasswordHashingService.VerifyPassword(String password, String hashedPassword) in D:\AIProjectTest\GamalCompanyApp\GamalComapnyApp\GamalComapany.Service\Authentication\PasswordHashingService.cs:line 61
2025-06-16 19:29:38.164 +03:00 [WRN] Login attempt with invalid password for user: Admin
2025-06-16 19:29:38.165 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-16 19:29:38.166 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 57.204ms
2025-06-16 19:29:38.178 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-16 19:29:38.179 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 401 null application/json; charset=utf-8 79.0519ms
2025-06-16 19:29:44.359 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-16 19:29:44.362 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 3.2651ms
2025-06-16 19:29:44.672 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-16 19:29:45.237 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 565.7818ms
2025-06-16 19:30:00.136 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 48
2025-06-16 19:30:00.140 +03:00 [INF] CORS policy execution successful.
2025-06-16 19:30:00.141 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-16 19:30:00.142 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-16 19:30:00.181 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-16 19:30:00.184 +03:00 [ERR] Error verifying password
System.FormatException: The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters.
   at System.Convert.FromBase64CharPtr(Char* inputPtr, Int32 inputLength)
   at System.Convert.FromBase64String(String s)
   at GamalComapany.Service.Authentication.PasswordHashingService.VerifyPassword(String password, String hashedPassword) in D:\AIProjectTest\GamalCompanyApp\GamalComapnyApp\GamalComapany.Service\Authentication\PasswordHashingService.cs:line 61
2025-06-16 19:30:00.186 +03:00 [WRN] Login attempt with invalid password for user: Admin
2025-06-16 19:30:00.188 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-16 19:30:00.190 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 23.46ms
2025-06-16 19:30:00.192 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-16 19:30:00.193 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 401 null application/json; charset=utf-8 56.6184ms
2025-06-16 19:30:09.344 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-16 19:30:09.354 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-16 19:30:09.357 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-16 19:30:09.361 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-16 19:30:09.362 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 18.5076ms
2025-06-16 19:30:12.580 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-16 19:30:12.585 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-16 19:30:12.586 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-16 19:30:12.588 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-16 19:30:12.590 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 9.0044ms
2025-06-16 19:30:51.862 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User - application/json 138
2025-06-16 19:30:51.865 +03:00 [INF] CORS policy execution successful.
2025-06-16 19:30:51.870 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-16 19:30:51.871 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-16 19:30:51.873 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-16 19:30:51.875 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User - 401 0 null 13.1966ms
2025-06-16 19:32:07.723 +03:00 [INF] Application is shutting down...
