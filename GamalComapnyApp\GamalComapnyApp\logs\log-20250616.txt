2025-06-16 19:10:12.224 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-16 19:10:12.270 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-16 19:10:12.272 +03:00 [INF] Hosting environment: Development
2025-06-16 19:10:12.273 +03:00 [INF] Content root path: D:\AIProjectTest\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-16 19:10:15.404 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/ - null null
2025-06-16 19:10:15.450 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/ - 404 0 null 47.811ms
2025-06-16 19:10:15.487 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/, Response status code: 404
2025-06-16 19:10:19.915 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-16 19:10:19.916 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-16 19:10:19.928 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 404 0 null 13.3581ms
2025-06-16 19:10:19.944 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 404 0 null 28.0029ms
2025-06-16 19:10:19.944 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js, Response status code: 404
2025-06-16 19:10:19.949 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/_vs/browserLink, Response status code: 404
2025-06-16 19:10:20.232 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-16 19:10:20.362 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 130.0207ms
2025-06-16 19:10:28.489 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-16 19:10:28.632 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 143.3691ms
2025-06-16 19:10:28.727 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-16 19:10:28.744 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 16.039ms
2025-06-16 19:10:36.316 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner - null null
2025-06-16 19:10:36.322 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API)'
2025-06-16 19:10:36.345 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-16 19:10:40.427 +03:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-16 19:10:40.457 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-16 19:10:40.506 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API) in 4154.3119ms
2025-06-16 19:10:40.508 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API)'
2025-06-16 19:10:40.517 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner - 404 null application/json; charset=utf-8 4200.6003ms
2025-06-16 19:11:33.425 +03:00 [INF] Application is shutting down...
