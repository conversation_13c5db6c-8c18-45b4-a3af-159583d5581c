﻿using GamalComapany.Service.Repositories.Interfaces;
using GamalCompany.Data.Context;
using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class UnitOfWorkOfService : IUnitOfWorkOfService
    {
        private readonly ApplicationDbContext _context;
        private IDbContextTransaction? _transaction;
      
        private IGenericRepository<Company>? companies;
        private IGenericRepository<ItemCategory>? _categories;
        private IGenericRepository<Item>? _items;
        private IGenericRepository<ItemImage>? _itemImages;
        private IGenericRepository<Unit>? _units;
        private IGenericRepository<Partner> partners;
        private IGenericRepository<CompanyDepartment> companyDepartments;
        private IGenericRepository<PartnerTransation> partnerTransation;
        public UnitOfWorkOfService(ApplicationDbContext context)
        {
            _context = context;
        }
        public IGenericRepository<ItemCategory> Categories => _categories ??= new GenericRepository<ItemCategory>(_context);

        public IGenericRepository<Item> Items => _items ??= new GenericRepository<Item>(_context);

        public IGenericRepository<ItemImage> ItemImages => _itemImages ??= new GenericRepository<ItemImage>(_context);

        public IGenericRepository<Unit> Unites => _units ??= new GenericRepository<Unit>(_context);

        public IGenericRepository<Company> Companies => companies ??= new GenericRepository<Company>(_context);

        public IGenericRepository<CompanyDepartment> CompanyDepartments => companyDepartments ??= new GenericRepository<CompanyDepartment>(_context);

        public IGenericRepository<Partner> Partners => partners ??= new GenericRepository<Partner>(_context);

        public IGenericRepository<PartnerTransation> PartnerTransations => partnerTransation ??= new GenericRepository<PartnerTransation>(_context);

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }
        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}
