﻿using GamalComapany.Service.Repositories.Interfaces;
using GamalCompany.Data.Context;
using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class UnitOfWorkOfService : IUnitOfWorkOfService
    {
        private readonly ApplicationDbContext _context;
        private IDbContextTransaction? _transaction;

        // Company Management
        private IGenericRepository<Company>? _companies;
        private IGenericRepository<CompanyDepartment>? _companyDepartments;

        // Partner Management
        private IGenericRepository<Partner>? _partners;
        private IGenericRepository<PartnerTransation>? _partnerTransations;

        // Inventory Management
        private IGenericRepository<ItemCategory>? _categories;
        private IGenericRepository<CategoryType>? _categoryTypes;
        private IGenericRepository<Item>? _items;
        private IGenericRepository<ItemImage>? _itemImages;
        private IGenericRepository<Unit>? _units;
        private IGenericRepository<InventoryTransaction>? _inventoryTransactions;

        // Invoice Management
        private IGenericRepository<InvoiceMaster>? _invoiceMasters;
        private IGenericRepository<InvoiceDetail>? _invoiceDetails;

        // Supplier/Customer Management
        private IGenericRepository<VanderType>? _vanderTypes;
        private IGenericRepository<SupplierCustomer>? _supplierCustomers;
        private IGenericRepository<SupplierCustomerTransaction>? _supplierCustomerTransactions;

        // Financial Management
        private IGenericRepository<Treasury>? _treasuries;
        private IGenericRepository<TreasuryTransaction>? _treasuryTransactions;
        private IGenericRepository<FinancialTransaction>? _financialTransactions;

        // Action Management
        private IGenericRepository<ActionType>? _actionTypes;
        private IGenericRepository<MainAction>? _mainActions;

        // User Management
        private IGenericRepository<User>? _users;
        private IGenericRepository<Module>? _modules;
        private IGenericRepository<UserPermission>? _userPermissions;
        private IGenericRepository<RefreshToken>? _refreshTokens;
        public UnitOfWorkOfService(ApplicationDbContext context)
        {
            _context = context;
        }

        // Company Management
        public IGenericRepository<Company> Companies => _companies ??= new GenericRepository<Company>(_context);
        public IGenericRepository<CompanyDepartment> CompanyDepartments => _companyDepartments ??= new GenericRepository<CompanyDepartment>(_context);

        // Partner Management
        public IGenericRepository<Partner> Partners => _partners ??= new GenericRepository<Partner>(_context);
        public IGenericRepository<PartnerTransation> PartnerTransations => _partnerTransations ??= new GenericRepository<PartnerTransation>(_context);

        // Inventory Management
        public IGenericRepository<ItemCategory> Categories => _categories ??= new GenericRepository<ItemCategory>(_context);
        public IGenericRepository<CategoryType> CategoryTypes => _categoryTypes ??= new GenericRepository<CategoryType>(_context);
        public IGenericRepository<Item> Items => _items ??= new GenericRepository<Item>(_context);
        public IGenericRepository<ItemImage> ItemImages => _itemImages ??= new GenericRepository<ItemImage>(_context);
        public IGenericRepository<Unit> Units => _units ??= new GenericRepository<Unit>(_context);
        public IGenericRepository<InventoryTransaction> InventoryTransactions => _inventoryTransactions ??= new GenericRepository<InventoryTransaction>(_context);

        // Invoice Management
        public IGenericRepository<InvoiceMaster> InvoiceMasters => _invoiceMasters ??= new GenericRepository<InvoiceMaster>(_context);
        public IGenericRepository<InvoiceDetail> InvoiceDetails => _invoiceDetails ??= new GenericRepository<InvoiceDetail>(_context);

        // Supplier/Customer Management
        public IGenericRepository<VanderType> VanderTypes => _vanderTypes ??= new GenericRepository<VanderType>(_context);
        public IGenericRepository<SupplierCustomer> SupplierCustomers => _supplierCustomers ??= new GenericRepository<SupplierCustomer>(_context);
        public IGenericRepository<SupplierCustomerTransaction> SupplierCustomerTransactions => _supplierCustomerTransactions ??= new GenericRepository<SupplierCustomerTransaction>(_context);

        // Financial Management
        public IGenericRepository<Treasury> Treasuries => _treasuries ??= new GenericRepository<Treasury>(_context);
        public IGenericRepository<TreasuryTransaction> TreasuryTransactions => _treasuryTransactions ??= new GenericRepository<TreasuryTransaction>(_context);
        public IGenericRepository<FinancialTransaction> FinancialTransactions => _financialTransactions ??= new GenericRepository<FinancialTransaction>(_context);

        // Action Management
        public IGenericRepository<ActionType> ActionTypes => _actionTypes ??= new GenericRepository<ActionType>(_context);
        public IGenericRepository<MainAction> MainActions => _mainActions ??= new GenericRepository<MainAction>(_context);

        // User Management
        public IGenericRepository<User> Users => _users ??= new GenericRepository<User>(_context);
        public IGenericRepository<Module> Modules => _modules ??= new GenericRepository<Module>(_context);
        public IGenericRepository<UserPermission> UserPermissions => _userPermissions ??= new GenericRepository<UserPermission>(_context);
        public IGenericRepository<RefreshToken> RefreshTokens => _refreshTokens ??= new GenericRepository<RefreshToken>(_context);

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }
        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}
