using System.Security.Cryptography;
using Microsoft.Extensions.Logging;

namespace GamalComapany.Service.Authentication
{
    public class PasswordHashingService : IPasswordHashingService
    {
        private readonly ILogger<PasswordHashingService> _logger;
        private const int SaltSize = 16; // 128 bits
        private const int KeySize = 32; // 256 bits
        private const int Iterations = 100000; // OWASP recommended minimum
        private static readonly HashAlgorithmName HashAlgorithm = HashAlgorithmName.SHA256;

        public PasswordHashingService(ILogger<PasswordHashingService> logger)
        {
            _logger = logger;
        }

        public string HashPassword(string password)
        {
            try
            {
                if (string.IsNullOrEmpty(password))
                    throw new ArgumentException("Password cannot be null or empty", nameof(password));

                // Generate a random salt
                using var rng = RandomNumberGenerator.Create();
                var salt = new byte[SaltSize];
                rng.GetBytes(salt);

                // Hash the password with the salt
                using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithm);
                var hash = pbkdf2.GetBytes(KeySize);

                // Combine salt and hash
                var hashBytes = new byte[SaltSize + KeySize];
                Array.Copy(salt, 0, hashBytes, 0, SaltSize);
                Array.Copy(hash, 0, hashBytes, SaltSize, KeySize);

                // Convert to base64 string
                var hashedPassword = Convert.ToBase64String(hashBytes);
                
                _logger.LogDebug("Password hashed successfully");
                return hashedPassword;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error hashing password");
                throw;
            }
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            try
            {
                if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hashedPassword))
                    return false;

                // Convert base64 string back to bytes
                var hashBytes = Convert.FromBase64String(hashedPassword);

                // Extract salt and hash
                var salt = new byte[SaltSize];
                Array.Copy(hashBytes, 0, salt, 0, SaltSize);

                var storedHash = new byte[KeySize];
                Array.Copy(hashBytes, SaltSize, storedHash, 0, KeySize);

                // Hash the provided password with the extracted salt
                using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithm);
                var computedHash = pbkdf2.GetBytes(KeySize);

                // Compare the hashes
                var isValid = CryptographicOperations.FixedTimeEquals(storedHash, computedHash);
                
                _logger.LogDebug("Password verification result: {IsValid}", isValid);
                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying password");
                return false;
            }
        }

        public bool NeedsRehash(string hashedPassword)
        {
            try
            {
                if (string.IsNullOrEmpty(hashedPassword))
                    return true;

                // Check if the hash format is current
                var hashBytes = Convert.FromBase64String(hashedPassword);
                return hashBytes.Length != (SaltSize + KeySize);
            }
            catch
            {
                return true; // If we can't parse it, it needs rehashing
            }
        }
    }
}
