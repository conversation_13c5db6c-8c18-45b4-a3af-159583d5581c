﻿using GamalCompany.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface IUnitOfWorkOfService : IDisposable
    {
        IGenericRepository<Company> Companies { get; }
        IGenericRepository<CompanyDepartment> CompanyDepartments { get; }
        IGenericRepository<Partner> Partners { get; }
        IGenericRepository<PartnerTransation> PartnerTransations { get; }

        IGenericRepository<ItemCategory> Categories { get; }
        IGenericRepository<Item> Items { get; }
        IGenericRepository<ItemImage> ItemImages { get; }
        IGenericRepository<Unit> Unites { get; }


        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }


}
