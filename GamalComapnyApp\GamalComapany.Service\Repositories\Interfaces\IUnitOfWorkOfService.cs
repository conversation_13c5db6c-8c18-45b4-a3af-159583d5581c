﻿using GamalCompany.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface IUnitOfWorkOfService : IDisposable
    {
        // Company Management
        IGenericRepository<Company> Companies { get; }
        IGenericRepository<CompanyDepartment> CompanyDepartments { get; }

        // Partner Management
        IGenericRepository<Partner> Partners { get; }
        IGenericRepository<PartnerTransation> PartnerTransations { get; }

        // Inventory Management
        IGenericRepository<ItemCategory> Categories { get; }
        IGenericRepository<CategoryType> CategoryTypes { get; }
        IGenericRepository<Item> Items { get; }
        IGenericRepository<ItemImage> ItemImages { get; }
        IGenericRepository<Unit> Units { get; }
        IGenericRepository<InventoryTransaction> InventoryTransactions { get; }

        // Invoice Management
        IGenericRepository<InvoiceMaster> InvoiceMasters { get; }
        IGenericRepository<InvoiceDetail> InvoiceDetails { get; }

        // Supplier/Customer Management
        IGenericRepository<VanderType> VanderTypes { get; }
        IGenericRepository<SupplierCustomer> SupplierCustomers { get; }
        IGenericRepository<SupplierCustomerTransaction> SupplierCustomerTransactions { get; }

        // Financial Management
        IGenericRepository<Treasury> Treasuries { get; }
        IGenericRepository<TreasuryTransaction> TreasuryTransactions { get; }
        IGenericRepository<FinancialTransaction> FinancialTransactions { get; }

        // Action Management
        IGenericRepository<ActionType> ActionTypes { get; }
        IGenericRepository<MainAction> MainActions { get; }

        // User Management
        IGenericRepository<User> Users { get; }
        IGenericRepository<Module> Modules { get; }
        IGenericRepository<UserPermission> UserPermissions { get; }

        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }


}
