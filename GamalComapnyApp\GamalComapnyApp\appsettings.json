{"ConnectionStrings": {"DefaultConnection": "Data Source=DESKTOP-G1KQMAJ\\SQLEXPRESS;Initial Catalog=DoorCompanyDb;User ID=***;Password=***;TrustServerCertificate=True;MultipleActiveResultSets=True;Integrated Security=False"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLongForSecurity!@#$%^&*()", "Issuer": "GamalCompanyApp", "Audience": "GamalCompanyApp", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ClockSkewMinutes": 5}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}