﻿using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.ItemDto;
using GamalComapany.Service.Repositories.Interfaces;
using GamalCompany.Data.Context;
using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class ItemService(IUnitOfWorkOfService iUnitOfWorkOfService) :ResponseHandler , IItemRepository
    {
        
        private readonly IUnitOfWorkOfService _iUnitOfWorkOfService = iUnitOfWorkOfService;        
       
    }
}
