﻿using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.ItemDto;
using GamalCompany.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface IItemRepository 
    {
       Task<ApiResponse<Item>> CreateProductAsync(CreateItemDto createItemDto);
    }
}
