﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class InventoryTransaction : BaseEntity
    {
        public int? InvoiceId { get; set; }
        public int? ActionTypeId { get; set; }
        public DateTime DateTransaction { get; set; }
        public int ItemId { get; set; }
        public int UnitId { get; set; }
        public int Quantity { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? UnitPrice { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TotalAmount { get; set; }

        [ForeignKey(nameof(InvoiceId))]
        public virtual InvoiceMaster InvoiceMasters { get; set; } = null!;

        [ForeignKey(nameof(ActionTypeId))]
        public MainAction MainAction { get; set; } = null!;

        [ForeignKey(nameof(UnitId))]
        public virtual Unit Units { get; set; } = null!;

        [ForeignKey(nameof(ItemId))]
        public virtual Item Items { get; set; } = null!;
    }
}
