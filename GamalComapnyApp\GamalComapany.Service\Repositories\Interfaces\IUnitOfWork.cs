﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface IUnitOfWork 
    {
        
        public IItemRepository ItemRepository { get; }
        public IUintRepository UintRepository { get; }
        public ICategoryRepository categoryRepository { get; }
        public ICompanyRepository companyRepository { get; }
     
    }
}
