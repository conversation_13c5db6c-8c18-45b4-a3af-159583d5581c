using System.ComponentModel.DataAnnotations;

namespace GamalComapany.Service.Dtos.FinancialDto
{
    public class CreateTreasuryDto
    {
        [Required(ErrorMessage = "اسم الخزينة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الخزينة يجب أن يكون أقل من 100 حرف")]
        public string NameEn { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;
    }

    public class UpdateTreasuryDto : CreateTreasuryDto
    {
        [Required(ErrorMessage = "معرف الخزينة مطلوب")]
        public int Id { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedBy { get; set; }
    }

    public class TreasuryResponseDto
    {
        public int Id { get; set; }
        public string NameEn { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal CurrentBalance { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateTreasuryTransactionDto
    {
        [Required(ErrorMessage = "معرف الخزينة مطلوب")]
        public int TreasuryId { get; set; }

        [Required(ErrorMessage = "نوع العملية مطلوب")]
        public int ActionTypeId { get; set; }

        [Required(ErrorMessage = "معرف المعاملة المالية مطلوب")]
        public int FinancialTransactionId { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransactionDate { get; set; }

        [StringLength(50, ErrorMessage = "رقم المرجع يجب أن يكون أقل من 50 حرف")]
        public string? ReferenceNumber { get; set; }

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
        public string? Notes { get; set; }

        public string? ImagePath { get; set; }
    }

    public class TreasuryTransactionResponseDto
    {
        public int Id { get; set; }
        public int TreasuryId { get; set; }
        public string TreasuryName { get; set; } = string.Empty;
        public int ActionTypeId { get; set; }
        public string ActionTypeName { get; set; } = string.Empty;
        public int FinancialTransactionId { get; set; }
        public decimal Amount { get; set; }
        public DateTime TransactionDate { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public string? ImagePath { get; set; }
        public decimal RunningBalance { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class CreateFinancialTransactionDto
    {
        [Required(ErrorMessage = "نوع المعاملة مطلوب")]
        public int TransactionTypeId { get; set; }

        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransactionDate { get; set; }

        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal? Amount { get; set; }

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        public int? InvoiceNumber { get; set; }
    }

    public class FinancialTransactionResponseDto
    {
        public int Id { get; set; }
        public int TransactionTypeId { get; set; }
        public string TransactionTypeName { get; set; } = string.Empty;
        public DateTime TransactionDate { get; set; }
        public decimal? Amount { get; set; }
        public string? Description { get; set; }
        public int? InvoiceNumber { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class TreasuryBalanceDto
    {
        public int TreasuryId { get; set; }
        public string TreasuryName { get; set; } = string.Empty;
        public decimal OpeningBalance { get; set; }
        public decimal TotalIncome { get; set; }
        public decimal TotalExpense { get; set; }
        public decimal ClosingBalance { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public List<TreasuryTransactionResponseDto> Transactions { get; set; } = new();
    }

    public class CashFlowDto
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal TotalIncome { get; set; }
        public decimal TotalExpense { get; set; }
        public decimal NetCashFlow { get; set; }
        public List<CashFlowCategoryDto> IncomeCategories { get; set; } = new();
        public List<CashFlowCategoryDto> ExpenseCategories { get; set; } = new();
    }

    public class CashFlowCategoryDto
    {
        public string CategoryName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
    }
}
