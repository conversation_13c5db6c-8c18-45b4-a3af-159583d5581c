﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class CategoryType : BaseName
    {
        public string? Code { get; set; }
        public string? Symbol {  get; set; }

        public int? SortOrder { get; set; }
        public virtual ICollection<ItemCategory> ItemCategories { get; set; } = new List<ItemCategory>();
    }
}
