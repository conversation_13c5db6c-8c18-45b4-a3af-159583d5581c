﻿using GamalComapany.Service.Dtos.CompanyDto;
using GamalComapany.Service.Repositories.Interfaces;
using GamalComapnyApp.API.Base;
using GamalCompany.Data.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;

namespace GamalComapnyApp.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CampanyController : AppControllerBase
    {
        public CampanyController(IUnitOfWork work) : base(work)
        {

        }

        [HttpGet]
        public async Task<IActionResult> GetCompany()
        {
            var response = await _work.CompanyRepository.GetCompany();
            return NewResult(response);
        }

        [HttpPost]
        public async Task<IActionResult> AddCompany(CreateCompanyDto company)
        {
            var response = await _work.CompanyRepository.AddCompany(company);
            return NewResult(response);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateCompany(UpdateCompanyDto company)
        {
            var response = await _work.CompanyRepository.UpdateCompany(company);
            return NewResult(response);
        }
       
        [HttpPost("department")]
        public async Task<IActionResult> AddDepartment(CearteDepartmentDto depatment)
        {
            var response = await _work.CompanyRepository.AddDepartment(depatment);
            return NewResult(response);
        }
     
        [HttpPut("department")]
        public async Task<IActionResult> UpdateDepartment(UpdateDepartmentDto depatmentcompany)
        {
            var response = await _work.CompanyRepository.UpdateDepartment(depatmentcompany);
            return NewResult(response);
        }  
        
        [HttpGet("department")]
        public async Task<IActionResult> GetListOFDepartment()
        {
            var response = await _work.CompanyRepository.GetDepartment();
            return NewResult(response);
        }


    }
}
