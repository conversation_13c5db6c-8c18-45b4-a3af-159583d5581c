﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class UserPermission : BaseEntity
    {
        public int UserId { get; set; }
        public int ModuleId { get; set; }
        public string? Permission { get; set; } // يمكن أن تكون enum في التطبيق

        [ForeignKey(nameof(UserId))]
        public User? User { get; set; }

        [ForeignKey(nameof(ModuleId))]
        public Module? Module { get; set; }
    }



}
