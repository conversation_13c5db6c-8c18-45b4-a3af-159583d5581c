﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Dtos.CompanyDto
{
    public class CompanyDepartmentDto
    {
        public int Id { get; set; }
        [Required(ErrorMessage = "من فضلك ادخل الاسم")]
        public string NameEn { get; set; } = string.Empty;
        [Required(ErrorMessage = "من فضلك ادخل الوصف")]
        public string Description { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Symbol { get; set; } = string.Empty;
    }
    public class CearteDepartmentDto : CompanyDepartmentDto
    {
        public int? CreateBy { get; set; } = 1;
    }
    public class UpdateDepartmentDto : CompanyDepartmentDto
    {
        public int? UpdateBy { get; set; } = 1;
        public DateTime UpdateAt { get; set; } = DateTime.Now;
    }

}
