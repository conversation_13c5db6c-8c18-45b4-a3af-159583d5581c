using System.ComponentModel.DataAnnotations;

namespace GamalComapany.Service.Dtos.SupplierCustomerDto
{
    public class CreateSupplierCustomerDto
    {
        [Required(ErrorMessage = "الاسم مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم يجب أن يكون أقل من 100 حرف")]
        public string NameEn { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "نوع المتعامل مطلوب")]
        public int VanderTypeId { get; set; }
    }

    public class UpdateSupplierCustomerDto : CreateSupplierCustomerDto
    {
        [Required(ErrorMessage = "المعرف مطلوب")]
        public int Id { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedBy { get; set; }
    }

    public class SupplierCustomerResponseDto
    {
        public int Id { get; set; }
        public string NameEn { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int? VanderTypeId { get; set; }
        public string VanderTypeName { get; set; } = string.Empty;
        public decimal CurrentBalance { get; set; }
        public decimal TotalDebit { get; set; }
        public decimal TotalCredit { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateSupplierCustomerTransactionDto
    {
        [Required(ErrorMessage = "معرف المتعامل مطلوب")]
        public int CustomerId { get; set; }

        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransactonDate { get; set; }

        [Required(ErrorMessage = "نوع المعاملة مطلوب")]
        public int TransactonTypeId { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        public int? InvoiceId { get; set; }
        public int? TreasuryTransactionId { get; set; }
    }

    public class SupplierCustomerTransactionResponseDto
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public DateTime TransactonDate { get; set; }
        public int TransactonTypeId { get; set; }
        public string TransactionTypeName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string? Description { get; set; }
        public int? InvoiceId { get; set; }
        public string? InvoiceNumber { get; set; }
        public int? TreasuryTransactionId { get; set; }
        public decimal RunningBalance { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class SupplierCustomerStatementDto
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string VanderTypeName { get; set; } = string.Empty;
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal TotalDebit { get; set; }
        public decimal TotalCredit { get; set; }
        public decimal ClosingBalance { get; set; }
        public List<SupplierCustomerTransactionResponseDto> Transactions { get; set; } = new();
    }

    public class CreateVanderTypeDto
    {
        [Required(ErrorMessage = "الاسم مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم يجب أن يكون أقل من 100 حرف")]
        public string NameEn { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;
    }

    public class UpdateVanderTypeDto : CreateVanderTypeDto
    {
        [Required(ErrorMessage = "المعرف مطلوب")]
        public int Id { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedBy { get; set; }
    }

    public class VanderTypeResponseDto
    {
        public int Id { get; set; }
        public string NameEn { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class SupplierCustomerSummaryDto
    {
        public int TotalSuppliers { get; set; }
        public int TotalCustomers { get; set; }
        public decimal TotalSuppliersBalance { get; set; }
        public decimal TotalCustomersBalance { get; set; }
        public decimal NetBalance { get; set; }
        public List<SupplierCustomerBalanceDto> TopSuppliers { get; set; } = new();
        public List<SupplierCustomerBalanceDto> TopCustomers { get; set; } = new();
    }

    public class SupplierCustomerBalanceDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public decimal Balance { get; set; }
        public DateTime LastTransactionDate { get; set; }
    }
}
