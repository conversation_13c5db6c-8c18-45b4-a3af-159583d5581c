﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Dtos.CompanyDto
{
    public class CreateCompanyDto
    {
        [Required(ErrorMessage = "من فضلك ادخل الاسم")]
        public string NameEn { get; set; } = string.Empty;
        [Required(ErrorMessage = "من فضلك ادخل الوصف")]
        public string Description { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Symbol { get; set; } = string.Empty;
        public string LogePath { get; set; } = string.Empty;
    }

    public class UpdateCompanyDto : CreateCompanyDto
    {
        public int Id { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedBy { get; set; }
    }
}
