using System.ComponentModel.DataAnnotations;

namespace GamalComapany.Service.Validators
{
    /// <summary>
    /// Validates that share percentages for all partners don't exceed 100%
    /// </summary>
    public class SharePercentageValidationAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is decimal percentage)
            {
                return percentage > 0 && percentage <= 100;
            }
            return true; // Let other validators handle null/type issues
        }

        public override string FormatErrorMessage(string name)
        {
            return "نسبة المشاركة يجب أن تكون بين 0.01 و 100";
        }
    }

    /// <summary>
    /// Validates that the date is not in the future
    /// </summary>
    public class NotFutureDateAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is DateTime date)
            {
                return date <= DateTime.Now;
            }
            return true;
        }

        public override string FormatErrorMessage(string name)
        {
            return "التاريخ لا يمكن أن يكون في المستقبل";
        }
    }

    /// <summary>
    /// Validates that the amount is positive
    /// </summary>
    public class PositiveAmountAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is decimal amount)
            {
                return amount > 0;
            }
            if (value is double doubleAmount)
            {
                return doubleAmount > 0;
            }
            if (value is int intAmount)
            {
                return intAmount > 0;
            }
            return true;
        }

        public override string FormatErrorMessage(string name)
        {
            return "المبلغ يجب أن يكون أكبر من صفر";
        }
    }

    /// <summary>
    /// Validates that minimum stock is less than maximum stock
    /// </summary>
    public class StockLevelValidationAttribute : ValidationAttribute
    {
        public string MaximumStockProperty { get; set; } = string.Empty;

        public override bool IsValid(object? value)
        {
            return true; // This will be handled in a custom validator
        }

        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            if (value is decimal minimumStock && !string.IsNullOrEmpty(MaximumStockProperty))
            {
                var maximumStockProperty = validationContext.ObjectType.GetProperty(MaximumStockProperty);
                if (maximumStockProperty != null)
                {
                    var maximumStockValue = maximumStockProperty.GetValue(validationContext.ObjectInstance);
                    if (maximumStockValue is decimal maximumStock)
                    {
                        if (minimumStock >= maximumStock)
                        {
                            return new ValidationResult("الحد الأدنى للمخزون يجب أن يكون أقل من الحد الأقصى");
                        }
                    }
                }
            }
            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// Validates that the user has sufficient permissions
    /// </summary>
    public class PermissionValidationAttribute : ValidationAttribute
    {
        public string RequiredPermission { get; set; } = string.Empty;

        public override bool IsValid(object? value)
        {
            // This will be implemented in the service layer
            return true;
        }
    }

    /// <summary>
    /// Validates that the treasury has sufficient balance for withdrawal
    /// </summary>
    public class SufficientBalanceAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            // This will be validated in the service layer with database access
            return true;
        }

        public override string FormatErrorMessage(string name)
        {
            return "الرصيد غير كافي لإتمام العملية";
        }
    }

    /// <summary>
    /// Validates that the item code is unique
    /// </summary>
    public class UniqueItemCodeAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            // This will be validated in the service layer with database access
            return true;
        }

        public override string FormatErrorMessage(string name)
        {
            return "كود الصنف موجود بالفعل";
        }
    }

    /// <summary>
    /// Validates that the username is unique
    /// </summary>
    public class UniqueUsernameAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            // This will be validated in the service layer with database access
            return true;
        }

        public override string FormatErrorMessage(string name)
        {
            return "اسم المستخدم موجود بالفعل";
        }
    }

    /// <summary>
    /// Validates Arabic text input
    /// </summary>
    public class ArabicTextAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is string text && !string.IsNullOrEmpty(text))
            {
                // Basic Arabic text validation - can be enhanced
                return text.Any(c => c >= 0x0600 && c <= 0x06FF);
            }
            return true;
        }

        public override string FormatErrorMessage(string name)
        {
            return "يجب إدخال نص باللغة العربية";
        }
    }

    /// <summary>
    /// Validates phone number format
    /// </summary>
    public class PhoneNumberAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is string phone && !string.IsNullOrEmpty(phone))
            {
                // Basic phone validation - can be enhanced based on requirements
                return phone.All(c => char.IsDigit(c) || c == '+' || c == '-' || c == ' ' || c == '(' || c == ')');
            }
            return true;
        }

        public override string FormatErrorMessage(string name)
        {
            return "تنسيق رقم الهاتف غير صحيح";
        }
    }
}
