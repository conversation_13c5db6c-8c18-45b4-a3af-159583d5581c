using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.InventoryDto;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface IInventoryRepository
    {
        // Inventory Transactions
        Task<ApiResponse<List<InventoryTransactionResponseDto>>> GetInventoryTransactionsAsync(int? itemId = null, DateTime? fromDate = null, DateTime? toDate = null);
        Task<ApiResponse<InventoryTransactionResponseDto>> CreateInventoryTransactionAsync(CreateInventoryTransactionDto transactionDto);
        Task<ApiResponse<bool>> DeleteInventoryTransactionAsync(int transactionId);
        
        // Stock Management
        Task<ApiResponse<List<ItemStockDto>>> GetItemsStockAsync();
        Task<ApiResponse<ItemStockDto>> GetItemStockAsync(int itemId);
        Task<ApiResponse<List<ItemStockDto>>> GetLowStockItemsAsync();
        
        // Inventory Movement Reports
        Task<ApiResponse<InventoryMovementDto>> GetInventoryMovementAsync(int itemId, DateTime fromDate, DateTime toDate);
        Task<ApiResponse<List<InventoryMovementDto>>> GetInventoryMovementReportAsync(DateTime fromDate, DateTime toDate);
        
        // Item Images
        Task<ApiResponse<List<ItemImageResponseDto>>> GetItemImagesAsync(int itemId);
        Task<ApiResponse<ItemImageResponseDto>> CreateItemImageAsync(CreateItemImageDto imageDto);
        Task<ApiResponse<bool>> DeleteItemImageAsync(int imageId);
        Task<ApiResponse<bool>> SetMainImageAsync(int itemId, int imageId);
        
        // Item Management Extensions
        Task<ApiResponse<ItemResponseDto>> UpdateItemAsync(UpdateItemDto updateItemDto);
        Task<ApiResponse<List<ItemResponseDto>>> GetItemsByCategoryAsync(int categoryId);
        Task<ApiResponse<List<ItemResponseDto>>> SearchItemsAsync(string searchTerm);
    }
}
