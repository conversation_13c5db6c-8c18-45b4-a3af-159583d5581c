﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class PartnerTransation : BaseEntity
    {
        public DateTime TransactionDate { get; set; }
        public int ActionDetailId { get; set; }  //Investment  //Withdrawal
        public int PartnerId { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal Amount { get; set; }
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public string? ImagePath { get; set; }
        public virtual Partner Partners { get; set; } = null!;

    }
}
