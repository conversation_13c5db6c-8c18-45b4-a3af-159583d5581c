﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class SupplierCustomerTransaction : BaseEntity
    {
        public int CustomerId { get; set; }
        public DateTime TransactonDate { get; set; } 
        public int TransactonTypeId { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal Amount { get; set; }
        public string? Description { get; set; }
        public int? InvoiceId { get; set; }
        public int? TreasuryTransactionId { get; set; }


        [ForeignKey(nameof(CustomerId))]
        public SupplierCustomer supplierCustomer { get; set; } = null!;

        [ForeignKey(nameof(TransactonTypeId))]
        public MainAction MainAction { get; set; } = null!;

        [ForeignKey(nameof(InvoiceId))]
        public InvoiceMaster InvoiceMaster { get; set; } = null!;

        [ForeignKey(nameof(TreasuryTransactionId))]
        public TreasuryTransaction TreasuryTransaction { get; set;} = null!;
    }
}
