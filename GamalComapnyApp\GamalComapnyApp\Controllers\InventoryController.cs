using GamalComapany.Service.Authorization;
using GamalComapany.Service.Dtos.InventoryDto;
using GamalComapany.Service.Repositories.Interfaces;
using GamalComapany.Service.Services;
using GamalComapnyApp.API.Base;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GamalComapnyApp.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class InventoryController : AppControllerBase
    {
        private readonly IImageService _imageService;

        public InventoryController(IUnitOfWork work, IImageService imageService) : base(work)
        {
            _imageService = imageService;
        }

        #region Inventory Transactions

        /// <summary>
        /// Get inventory transactions with optional filtering
        /// </summary>
        [HttpGet("transactions")]
        [RequireInventoryRead]
        public async Task<IActionResult> GetInventoryTransactions(
            [FromQuery] int? itemId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            var response = await _work.InventoryRepository.GetInventoryTransactionsAsync(itemId, fromDate, toDate);
            return NewResult(response);
        }

        /// <summary>
        /// Create new inventory transaction
        /// </summary>
        [HttpPost("transactions")]
        [RequireInventoryWrite]
        public async Task<IActionResult> CreateInventoryTransaction(CreateInventoryTransactionDto transactionDto)
        {
            var response = await _work.InventoryRepository.CreateInventoryTransactionAsync(transactionDto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete inventory transaction
        /// </summary>
        [HttpDelete("transactions/{transactionId}")]
        [RequireInventoryWrite]
        public async Task<IActionResult> DeleteInventoryTransaction(int transactionId)
        {
            var response = await _work.InventoryRepository.DeleteInventoryTransactionAsync(transactionId);
            return NewResult(response);
        }

        #endregion

        #region Stock Management

        /// <summary>
        /// Get stock levels for all items
        /// </summary>
        [HttpGet("stock")]
        [RequireInventoryRead]
        public async Task<IActionResult> GetItemsStock()
        {
            var response = await _work.InventoryRepository.GetItemsStockAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Get stock level for specific item
        /// </summary>
        [HttpGet("stock/{itemId}")]
        [RequireInventoryRead]
        public async Task<IActionResult> GetItemStock(int itemId)
        {
            var response = await _work.InventoryRepository.GetItemStockAsync(itemId);
            return NewResult(response);
        }

        /// <summary>
        /// Get items with low stock levels
        /// </summary>
        [HttpGet("stock/low")]
        [RequireInventoryRead]
        public async Task<IActionResult> GetLowStockItems()
        {
            var response = await _work.InventoryRepository.GetLowStockItemsAsync();
            return NewResult(response);
        }

        #endregion

        #region Inventory Movement Reports

        /// <summary>
        /// Get inventory movement for specific item
        /// </summary>
        [HttpGet("movement/{itemId}")]
        [RequireInventoryRead]
        public async Task<IActionResult> GetInventoryMovement(
            int itemId,
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate)
        {
            var response = await _work.InventoryRepository.GetInventoryMovementAsync(itemId, fromDate, toDate);
            return NewResult(response);
        }

        /// <summary>
        /// Get inventory movement report for all items
        /// </summary>
        [HttpGet("movement-report")]
        [RequireInventoryRead]
        public async Task<IActionResult> GetInventoryMovementReport(
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate)
        {
            var response = await _work.InventoryRepository.GetInventoryMovementReportAsync(fromDate, toDate);
            return NewResult(response);
        }

        #endregion

        #region Item Management

        /// <summary>
        /// Update item
        /// </summary>
        [HttpPut("items")]
        [RequireInventoryWrite]
        public async Task<IActionResult> UpdateItem(UpdateItemDto updateItemDto)
        {
            var response = await _work.InventoryRepository.UpdateItemAsync(updateItemDto);
            return NewResult(response);
        }

        /// <summary>
        /// Get items by category
        /// </summary>
        [HttpGet("items/category/{categoryId}")]
        [RequireInventoryRead]
        public async Task<IActionResult> GetItemsByCategory(int categoryId)
        {
            var response = await _work.InventoryRepository.GetItemsByCategoryAsync(categoryId);
            return NewResult(response);
        }

        /// <summary>
        /// Search items
        /// </summary>
        [HttpGet("items/search")]
        [RequireInventoryRead]
        public async Task<IActionResult> SearchItems([FromQuery] string searchTerm)
        {
            var response = await _work.InventoryRepository.SearchItemsAsync(searchTerm);
            return NewResult(response);
        }

        #endregion

        #region Item Images

        /// <summary>
        /// Get item images
        /// </summary>
        [HttpGet("items/{itemId}/images")]
        [RequireInventoryRead]
        public async Task<IActionResult> GetItemImages(int itemId)
        {
            var response = await _work.InventoryRepository.GetItemImagesAsync(itemId);
            return NewResult(response);
        }

        /// <summary>
        /// Upload item image
        /// </summary>
        [HttpPost("items/{itemId}/images")]
        [RequireInventoryWrite]
        public async Task<IActionResult> UploadItemImage(int itemId, IFormFile file, [FromForm] bool isMain = false)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("لم يتم تحديد ملف");

                if (!await _imageService.ValidateImageAsync(file))
                    return BadRequest("ملف الصورة غير صالح");

                // Upload image
                var imagePath = await _imageService.UploadItemImageAsync(file, itemId, isMain);

                // Create image record
                var imageDto = new CreateItemImageDto
                {
                    ItemId = itemId,
                    ImagePath = imagePath,
                    ImageName = file.FileName,
                    IsMain = isMain
                };

                var response = await _work.InventoryRepository.CreateItemImageAsync(imageDto);
                return NewResult(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء رفع الصورة: {ex.Message}");
            }
        }

        /// <summary>
        /// Delete item image
        /// </summary>
        [HttpDelete("items/images/{imageId}")]
        [RequireInventoryWrite]
        public async Task<IActionResult> DeleteItemImage(int imageId)
        {
            var response = await _work.InventoryRepository.DeleteItemImageAsync(imageId);
            return NewResult(response);
        }

        /// <summary>
        /// Set main item image
        /// </summary>
        [HttpPut("items/{itemId}/images/{imageId}/set-main")]
        [RequireInventoryWrite]
        public async Task<IActionResult> SetMainImage(int itemId, int imageId)
        {
            var response = await _work.InventoryRepository.SetMainImageAsync(itemId, imageId);
            return NewResult(response);
        }

        #endregion

        #region Barcode Management

        /// <summary>
        /// Generate barcode for item
        /// </summary>
        [HttpPost("items/{itemId}/generate-barcode")]
        [RequireInventoryWrite]
        public async Task<IActionResult> GenerateBarcode(int itemId)
        {
            try
            {
                // Simple barcode generation - can be enhanced with proper barcode libraries
                var barcode = $"ITM{itemId:D8}{DateTime.Now:yyyyMMdd}";
                
                // Update item with barcode
                var item = await _work.ItemRepository.GetItemById(itemId);
                if (!item.Succeeded)
                    return NewResult(item);

                var updateDto = new UpdateItemDto
                {
                    Id = itemId,
                    NameEn = item.Data.NameEn,
                    NameAr = item.Data.NameAr,
                    Description = item.Data.Description,
                    ItemCode = item.Data.ItemCode,
                    CategoryId = item.Data.CategoryId,
                    Barcode = barcode,
                    UnitId = item.Data.UnitId,
                    StandardCost = item.Data.StandardCost,
                    MinimumStock = item.Data.MinimumStock,
                    MaximumStock = item.Data.MaximumStock,
                    ReorderLevel = item.Data.ReorderLevel,
                    SortOrder = item.Data.SortOrder,
                    ItemType = item.Data.ItemType,
                    UpdatedAt = DateTime.Now
                };

                var response = await _work.InventoryRepository.UpdateItemAsync(updateDto);
                return NewResult(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء إنشاء الباركود: {ex.Message}");
            }
        }

        #endregion

        #region Import/Export

        /// <summary>
        /// Export items to CSV
        /// </summary>
        [HttpGet("items/export")]
        [RequireInventoryRead]
        public async Task<IActionResult> ExportItems()
        {
            try
            {
                var items = await _work.ItemRepository.GetItems();
                if (!items.Succeeded)
                    return NewResult(items);

                // Simple CSV export - can be enhanced with proper CSV libraries
                var csv = "ID,Name,Code,Category,Unit,StandardCost,CurrentStock\n";
                
                foreach (var item in items.Data)
                {
                    var stockResponse = await _work.InventoryRepository.GetItemStockAsync(item.Id);
                    var currentStock = stockResponse.Succeeded ? stockResponse.Data.CurrentStock : 0;
                    
                    csv += $"{item.Id},{item.NameEn},{item.ItemCode},{item.CategoryId},{item.UnitId},{item.StandardCost},{currentStock}\n";
                }

                var bytes = System.Text.Encoding.UTF8.GetBytes(csv);
                return File(bytes, "text/csv", $"items_export_{DateTime.Now:yyyyMMdd}.csv");
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء تصدير البيانات: {ex.Message}");
            }
        }

        #endregion
    }
}
