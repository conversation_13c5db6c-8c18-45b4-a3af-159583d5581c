using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.SupplierCustomerDto;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface ISupplierCustomerRepository
    {
        // Supplier/Customer Management
        Task<ApiResponse<List<SupplierCustomerResponseDto>>> GetAllSuppliersCustomersAsync();
        Task<ApiResponse<List<SupplierCustomerResponseDto>>> GetSuppliersByTypeAsync(int vanderTypeId);
        Task<ApiResponse<SupplierCustomerResponseDto>> GetSupplierCustomerByIdAsync(int id);
        Task<ApiResponse<SupplierCustomerResponseDto>> CreateSupplierCustomerAsync(CreateSupplierCustomerDto supplierCustomerDto);
        Task<ApiResponse<SupplierCustomerResponseDto>> UpdateSupplierCustomerAsync(UpdateSupplierCustomerDto supplierCustomerDto);
        Task<ApiResponse<bool>> DeleteSupplierCustomerAsync(int id);
        
        // Supplier/Customer Transactions
        Task<ApiResponse<List<SupplierCustomerTransactionResponseDto>>> GetSupplierCustomerTransactionsAsync(int customerId);
        Task<ApiResponse<List<SupplierCustomerTransactionResponseDto>>> GetAllTransactionsAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<ApiResponse<SupplierCustomerTransactionResponseDto>> CreateSupplierCustomerTransactionAsync(CreateSupplierCustomerTransactionDto transactionDto);
        Task<ApiResponse<bool>> DeleteSupplierCustomerTransactionAsync(int transactionId);
        
        // Account Statements
        Task<ApiResponse<SupplierCustomerStatementDto>> GetSupplierCustomerStatementAsync(int customerId, DateTime fromDate, DateTime toDate);
        Task<ApiResponse<List<SupplierCustomerStatementDto>>> GetAllStatementsAsync(DateTime fromDate, DateTime toDate);
        
        // Balance Calculations
        Task<ApiResponse<decimal>> GetSupplierCustomerBalanceAsync(int customerId);
        Task<ApiResponse<SupplierCustomerSummaryDto>> GetSupplierCustomerSummaryAsync();
        
        // Vander Types Management
        Task<ApiResponse<List<VanderTypeResponseDto>>> GetAllVanderTypesAsync();
        Task<ApiResponse<VanderTypeResponseDto>> GetVanderTypeByIdAsync(int id);
        Task<ApiResponse<VanderTypeResponseDto>> CreateVanderTypeAsync(CreateVanderTypeDto vanderTypeDto);
        Task<ApiResponse<VanderTypeResponseDto>> UpdateVanderTypeAsync(UpdateVanderTypeDto vanderTypeDto);
        Task<ApiResponse<bool>> DeleteVanderTypeAsync(int id);
    }
}
