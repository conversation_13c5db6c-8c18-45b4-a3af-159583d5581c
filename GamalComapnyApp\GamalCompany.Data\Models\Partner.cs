﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class Partner : BaseName
    {
        [Column(TypeName = "decimal(18,4)")]
        public decimal? InitialInvestment { get; set; }
        // public decimal? CapitalShare { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal? SharePercentage { get; set; }
        public virtual ICollection<PartnerTransation> PartnerTransations { get; set; } = new List<PartnerTransation>();
    }
}
