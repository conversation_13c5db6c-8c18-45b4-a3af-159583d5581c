{"Version": 1, "WorkspaceRootPath": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3B68E784-7A2A-4FCD-B6F2-7733D7DC56C2}|GamalComapnyApp\\GamalComapnyApp.API.csproj|d:\\aiprojecttest\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapnyapp\\controllers\\imagecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B68E784-7A2A-4FCD-B6F2-7733D7DC56C2}|GamalComapnyApp\\GamalComapnyApp.API.csproj|solutionrelative:gamalcomapnyapp\\controllers\\imagecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\aiprojecttest\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\repositories\\implementations\\inventoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\repositories\\implementations\\inventoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|d:\\aiprojecttest\\gamalcompanyapp\\gamalcomapnyapp\\gamalcompany.data\\models\\itemimage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|solutionrelative:gamalcompany.data\\models\\itemimage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "ImageController.cs", "DocumentMoniker": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\Controllers\\ImageController.cs", "RelativeDocumentMoniker": "GamalComapnyApp\\Controllers\\ImageController.cs", "ToolTip": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\Controllers\\ImageController.cs", "RelativeToolTip": "GamalComapnyApp\\Controllers\\ImageController.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAYwBAAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T17:01:30.995Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ItemImage.cs", "DocumentMoniker": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Models\\ItemImage.cs", "RelativeDocumentMoniker": "GamalCompany.Data\\Models\\ItemImage.cs", "ToolTip": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Models\\ItemImage.cs", "RelativeToolTip": "GamalCompany.Data\\Models\\ItemImage.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAAABIAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T16:59:59.686Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "InventoryService.cs", "DocumentMoniker": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\InventoryService.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Repositories\\Implementations\\InventoryService.cs", "ToolTip": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\InventoryService.cs", "RelativeToolTip": "GamalComapany.Service\\Repositories\\Implementations\\InventoryService.cs", "ViewState": "AgIAANIBAAAAAAAAAAAYwNgBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T16:57:13.642Z", "EditorCaption": ""}]}]}]}