﻿using GamalComapany.Service.Repositories.Implementations;
using GamalComapany.Service.Repositories.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.ModuleDependencies
{
    public static class ModuleRepositoriesDependencies
    {
        public static IServiceCollection AddRepositoriesDependencies(this IServiceCollection services)
        {

            services.AddTransient(typeof(IGenericRepository<>), typeof(GenericRepository<>));
            services.AddScoped<IUnitOfWorkOfService, UnitOfWorkOfService>();
            services.AddTransient<IUnitOfWork, UnitOfWork>();


            return services;
        }
    }
}
