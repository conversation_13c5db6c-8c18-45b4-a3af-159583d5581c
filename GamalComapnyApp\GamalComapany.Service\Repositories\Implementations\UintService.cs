﻿using GamalComapany.Service.Dtos;
using GamalComapany.Service.Repositories.Interfaces;
using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class UintService(IUnitOfWorkOfService unitOfWorkOfService) : ResponseHandler, IUintRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWorkOfService = unitOfWorkOfService;

        public async Task<ApiResponse<List<Unit>>> GetUnit()
        {
          var data = await _unitOfWorkOfService.Unites.GetTableNoTracking().ToListAsync();
            if (data == null || data.Count == 0) return NotFound<List<Unit>>("لا توجد بيانات");
            return Success(data);
        }
    }
}
