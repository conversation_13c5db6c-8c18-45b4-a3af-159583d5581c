﻿using GamalComapany.Service.Dtos;
using GamalComapany.Service.Repositories.Interfaces;
using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class CategoryService : ResponseH<PERSON><PERSON>,  ICategoryRepository
    {
        private IUnitOfWorkOfService _work;

        public CategoryService(IUnitOfWorkOfService work)
        {
            _work = work;
        }

        public async Task<ApiResponse<List<ItemCategory>>> GetListOfCategory()
        {
            var data = await _work.Categories.GetTableNoTracking().ToListAsync();
            if (data == null || data.Count == 0) return NotFound<List<ItemCategory>>("لا توجد بيانات");  
            return Success(data);
        }
    }
}
