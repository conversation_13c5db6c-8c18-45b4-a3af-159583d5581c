﻿using GamalComapany.Service.Repositories.Interfaces;
using AutoMapper;
using Microsoft.Extensions.Logging;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly IUnitOfWorkOfService _work;
        private readonly IMapper _mapper;
        private readonly ILoggerFactory _loggerFactory;

        // Existing Repositories
        public IItemRepository ItemRepository { get; }
        public IUintRepository UintRepository { get; }
        public ICategoryRepository CategoryRepository { get; }
        public ICompanyRepository CompanyRepository { get; }

        // New Repositories
        public IPartnerRepository PartnerRepository { get; }
        public IInventoryRepository InventoryRepository { get; }
        public IFinancialRepository FinancialRepository { get; }
        public ISupplierCustomerRepository SupplierCustomerRepository { get; }
        public IUserRepository UserRepository { get; }

        public UnitOfWork(
            IUnitOfWorkOfService iUnitOfWorkOfService,
            IMapper mapper,
            ILoggerFactory loggerFactory,
            IItemRepository itemRepository,
            IPartnerRepository partnerRepository,
            IUserRepository userRepository,
            IInventoryRepository inventoryRepository)
        {
            _work = iUnitOfWorkOfService;
            _mapper = mapper;
            _loggerFactory = loggerFactory;

            // Initialize repositories via dependency injection
            ItemRepository = itemRepository;
            PartnerRepository = partnerRepository;
            UserRepository = userRepository;
            InventoryRepository = inventoryRepository;

            // Initialize legacy repositories (to be migrated to DI)
            UintRepository = new UintService(_work);
            CategoryRepository = new CategoryService(_work);
            CompanyRepository = new CompanyService(_work);

            // TODO: Initialize other repositories when implemented
            FinancialRepository = null!; // new FinancialService(_work, _mapper, _loggerFactory.CreateLogger<FinancialService>());
            SupplierCustomerRepository = null!; // new SupplierCustomerService(_work, _mapper, _loggerFactory.CreateLogger<SupplierCustomerService>());
        }
    }
}
