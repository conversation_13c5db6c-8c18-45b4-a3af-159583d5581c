﻿using GamalComapany.Service.Repositories.Interfaces;
using GamalCompany.Data.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly IUnitOfWorkOfService _work;
      
        public IItemRepository ItemRepository { get; }

        public IUintRepository UintRepository { get; }

        public ICategoryRepository categoryRepository { get; }
        public ICompanyRepository companyRepository { get; }


        public UnitOfWork(IUnitOfWorkOfService iUnitOfWorkOfService)
        {
            _work = iUnitOfWorkOfService;
            ItemRepository = new ItemService(_work);
            UintRepository = new UintService(_work);
            categoryRepository = new CategoryService(_work);
            companyRepository = new CompanyService(_work);
        }


    }
}
