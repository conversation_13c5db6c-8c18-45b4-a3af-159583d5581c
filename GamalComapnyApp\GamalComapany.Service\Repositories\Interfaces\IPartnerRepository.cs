using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.PartnerDto;
using GamalCompany.Data.Models;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface IPartnerRepository
    {
        Task<ApiResponse<List<PartnerResponseDto>>> GetAllPartnersAsync();
        Task<ApiResponse<PartnerResponseDto>> GetPartnerByIdAsync(int id);
        Task<ApiResponse<PartnerResponseDto>> CreatePartnerAsync(CreatePartnerDto createPartnerDto);
        Task<ApiResponse<PartnerResponseDto>> UpdatePartnerAsync(UpdatePartnerDto updatePartnerDto);
        Task<ApiResponse<bool>> DeletePartnerAsync(int id);
        Task<ApiResponse<List<PartnerCapitalCalculationDto>>> CalculatePartnersCapitalAsync();
        Task<ApiResponse<bool>> RecalculateSharePercentagesAsync();
        Task<ApiResponse<List<PartnerProfitDistributionDto>>> CalculateProfitDistributionAsync(decimal totalProfit);
        
        // Partner Transactions
        Task<ApiResponse<List<PartnerTransactionResponseDto>>> GetPartnerTransactionsAsync(int partnerId);
        Task<ApiResponse<PartnerTransactionResponseDto>> CreatePartnerTransactionAsync(PartnerTransactionDto transactionDto);
        Task<ApiResponse<bool>> DeletePartnerTransactionAsync(int transactionId);
    }
}
