{"Version": 1, "WorkspaceRootPath": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3B68E784-7A2A-4FCD-B6F2-7733D7DC56C2}|GamalComapnyApp\\GamalComapnyApp.API.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapnyapp\\controllers\\campanycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B68E784-7A2A-4FCD-B6F2-7733D7DC56C2}|GamalComapnyApp\\GamalComapnyApp.API.csproj|solutionrelative:gamalcomapnyapp\\controllers\\campanycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\repositories\\implementations\\companyservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\repositories\\implementations\\companyservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\repositories\\interfaces\\icompanyrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\repositories\\interfaces\\icompanyrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\dtos\\companydto\\companydepartmentdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\dtos\\companydto\\companydepartmentdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\dtos\\companydto\\companydto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\dtos\\companydto\\companydto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\repositories\\implementations\\categoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\repositories\\implementations\\categoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\repositories\\interfaces\\icategoryrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\repositories\\interfaces\\icategoryrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcompany.data\\models\\company.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|solutionrelative:gamalcompany.data\\models\\company.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\repositories\\implementations\\uintservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\repositories\\implementations\\uintservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\dtos\\itemdto\\itemdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\dtos\\itemdto\\itemdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B68E784-7A2A-4FCD-B6F2-7733D7DC56C2}|GamalComapnyApp\\GamalComapnyApp.API.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapnyapp\\controllers\\categroycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B68E784-7A2A-4FCD-B6F2-7733D7DC56C2}|GamalComapnyApp\\GamalComapnyApp.API.csproj|solutionrelative:gamalcomapnyapp\\controllers\\categroycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B68E784-7A2A-4FCD-B6F2-7733D7DC56C2}|GamalComapnyApp\\GamalComapnyApp.API.csproj|d:\\source code\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapnyapp\\controllers\\unitcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B68E784-7A2A-4FCD-B6F2-7733D7DC56C2}|GamalComapnyApp\\GamalComapnyApp.API.csproj|solutionrelative:gamalcomapnyapp\\controllers\\unitcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 4, "Title": "CompanyDto.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Dtos\\CompanyDto\\CompanyDto.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Dtos\\CompanyDto\\CompanyDto.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Dtos\\CompanyDto\\CompanyDto.cs", "RelativeToolTip": "GamalComapany.Service\\Dtos\\CompanyDto\\CompanyDto.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAkAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T13:55:56.758Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ICompanyRepository.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Interfaces\\ICompanyRepository.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Repositories\\Interfaces\\ICompanyRepository.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Interfaces\\ICompanyRepository.cs", "RelativeToolTip": "GamalComapany.Service\\Repositories\\Interfaces\\ICompanyRepository.cs", "ViewState": "AgIAAA4AAAAAAAAAAAA0wBIAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T13:54:59.282Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "CampanyController.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\Controllers\\CampanyController.cs", "RelativeDocumentMoniker": "GamalComapnyApp\\Controllers\\CampanyController.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\Controllers\\CampanyController.cs*", "RelativeToolTip": "GamalComapnyApp\\Controllers\\CampanyController.cs*", "ViewState": "AgIAAC8AAAAAAAAAAAAAADYAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T13:37:54.75Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CompanyService.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\CompanyService.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Repositories\\Implementations\\CompanyService.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\CompanyService.cs", "RelativeToolTip": "GamalComapany.Service\\Repositories\\Implementations\\CompanyService.cs", "ViewState": "AgIAABcAAAAAAAAAAAAmwEsAAABQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T14:35:25.944Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "CategoryService.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\CategoryService.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Repositories\\Implementations\\CategoryService.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\CategoryService.cs", "RelativeToolTip": "GamalComapany.Service\\Repositories\\Implementations\\CategoryService.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAAAA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T14:35:24.708Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "CompanyDepartmentDto.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Dtos\\CompanyDto\\CompanyDepartmentDto.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Dtos\\CompanyDto\\CompanyDepartmentDto.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Dtos\\CompanyDto\\CompanyDepartmentDto.cs", "RelativeToolTip": "GamalComapany.Service\\Dtos\\CompanyDto\\CompanyDepartmentDto.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAABkAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T14:29:20.495Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ICategoryRepository.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Interfaces\\ICategoryRepository.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Repositories\\Interfaces\\ICategoryRepository.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Interfaces\\ICategoryRepository.cs", "RelativeToolTip": "GamalComapany.Service\\Repositories\\Interfaces\\ICategoryRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T14:35:20.445Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ItemDto.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Dtos\\ItemDto\\ItemDto.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Dtos\\ItemDto\\ItemDto.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Dtos\\ItemDto\\ItemDto.cs", "RelativeToolTip": "GamalComapany.Service\\Dtos\\ItemDto\\ItemDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T14:09:35.796Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "Company.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Models\\Company.cs", "RelativeDocumentMoniker": "GamalCompany.Data\\Models\\Company.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Models\\Company.cs", "RelativeToolTip": "GamalCompany.Data\\Models\\Company.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T13:56:18.765Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "UintService.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\UintService.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Repositories\\Implementations\\UintService.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\UintService.cs", "RelativeToolTip": "GamalComapany.Service\\Repositories\\Implementations\\UintService.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAowBgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T13:41:25.091Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "UnitController.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\Controllers\\UnitController.cs", "RelativeDocumentMoniker": "GamalComapnyApp\\Controllers\\UnitController.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\Controllers\\UnitController.cs", "RelativeToolTip": "GamalComapnyApp\\Controllers\\UnitController.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAAABIAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T13:38:04.443Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "CategroyController.cs", "DocumentMoniker": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\Controllers\\CategroyController.cs", "RelativeDocumentMoniker": "GamalComapnyApp\\Controllers\\CategroyController.cs", "ToolTip": "D:\\Source Code\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\Controllers\\CategroyController.cs", "RelativeToolTip": "GamalComapnyApp\\Controllers\\CategroyController.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAAABMAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T13:37:55.939Z", "EditorCaption": ""}]}]}]}