{"Version": 1, "WorkspaceRootPath": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\aiprojecttest\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\mapping\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\mapping\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\aiprojecttest\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\repositories\\implementations\\itemservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\repositories\\implementations\\itemservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\aiprojecttest\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\repositories\\interfaces\\iitemrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\repositories\\interfaces\\iitemrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MappingProfile.cs", "DocumentMoniker": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Mapping\\MappingProfile.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Mapping\\MappingProfile.cs", "ToolTip": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Mapping\\MappingProfile.cs", "RelativeToolTip": "GamalComapany.Service\\Mapping\\MappingProfile.cs", "ViewState": "AgIAAEQAAAAAAAAAAAAlwFEAAABSAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T16:09:22.599Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "IItemRepository.cs", "DocumentMoniker": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Interfaces\\IItemRepository.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Repositories\\Interfaces\\IItemRepository.cs", "ToolTip": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Interfaces\\IItemRepository.cs", "RelativeToolTip": "GamalComapany.Service\\Repositories\\Interfaces\\IItemRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T16:09:09.781Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ItemService.cs", "DocumentMoniker": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\ItemService.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Repositories\\Implementations\\ItemService.cs", "ToolTip": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\ItemService.cs", "RelativeToolTip": "GamalComapany.Service\\Repositories\\Implementations\\ItemService.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAawBMAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T16:08:39.371Z", "EditorCaption": ""}]}]}]}