﻿using GamalComapany.Service.Repositories.Interfaces;
using GamalComapnyApp.API.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace GamalComapnyApp.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CategroyController: AppControllerBase
    {
        public CategroyController(IUnitOfWork work) : base(work)
        {

        }

        [HttpGet]
        public async Task<IActionResult> GetCategory()
        {
            var response = await _work.CategoryRepository.GetListOfCategory();
            return NewResult(response);
        }
       
    }
}
