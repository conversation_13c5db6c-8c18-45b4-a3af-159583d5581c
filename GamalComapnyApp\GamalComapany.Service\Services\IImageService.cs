using Microsoft.AspNetCore.Http;

namespace GamalComapany.Service.Services
{
    public interface IImageService
    {
        Task<string> UploadImageAsync(IFormFile file, string folder, string? fileName = null);
        Task<string> UploadUserProfileImageAsync(IFormFile file, int userId);
        Task<string> UploadItemImageAsync(IFormFile file, int itemId, bool isMain = false);
        Task<bool> DeleteImageAsync(string imagePath);
        Task<bool> DeleteUserProfileImageAsync(int userId);
        Task<bool> DeleteItemImageAsync(string imagePath);
        Task<byte[]?> GetImageAsync(string imagePath);
        Task<string> ResizeImageAsync(string imagePath, int width, int height);
        Task<List<string>> GetSupportedFormats();
        Task<bool> ValidateImageAsync(IFormFile file);
        Task<long> GetMaxFileSizeAsync();
        Task<string> GenerateUniqueFileNameAsync(string originalFileName);
        Task<bool> ImageExistsAsync(string imagePath);
        Task CleanupOrphanedImagesAsync();
    }
}
