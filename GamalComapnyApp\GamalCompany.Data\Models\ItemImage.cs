﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class ItemImage : BaseEntity
    {
        public int ItemId { get; set; }
        public string? ImageUrl { get; set; }
        public string? ImageTitle { get; set; }
        public string? ImageDescription { get; set; }
        public int IsPrimary { get; set; }
        public int SortOrder { get; set; }

        [ForeignKey(nameof(ItemId))]
        public virtual Item? Items { get; set; }
    }
}
