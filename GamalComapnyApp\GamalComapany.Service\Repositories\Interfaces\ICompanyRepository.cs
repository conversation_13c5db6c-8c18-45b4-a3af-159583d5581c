﻿using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.CompanyDto;
using GamalCompany.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface ICompanyRepository
    {
        Task<ApiResponse<Company>> GetCompany();
        Task<ApiResponse<Company>> AddCompany(CreateCompanyDto company);
        Task<ApiResponse<Company>> UpdateCompany(UpdateCompanyDto company);
        Task<ApiResponse<CompanyDepartment>> AddDepartment(CearteDepartmentDto Depatment);
        Task<ApiResponse<CompanyDepartment>> UpdateDepartment(UpdateDepartmentDto depatmentcompany);
        Task<ApiResponse<List<CompanyDepartmentDto>>> GetDepartment();
    }
}
